/**
 * 当贝AI聊天界面 - Markdown渲染器
 * 提供Markdown格式的文本渲染功能
 */

class MarkdownRenderer {
  constructor() {
    this.codeBlockCounter = 0;
  }

  /**
   * 渲染Markdown文本
   * @param {string} text - 要渲染的Markdown文本
   * @returns {string} 渲染后的HTML
   */
  render(text) {
    if (!text) return '';
    
    let html = text;
    
    // 转义HTML特殊字符（除了我们要处理的Markdown语法）
    html = this.escapeHtml(html);
    
    // 处理代码块（必须在其他处理之前）
    html = this.renderCodeBlocks(html);
    
    // 处理行内代码
    html = this.renderInlineCode(html);
    
    // 处理标题
    html = this.renderHeaders(html);
    
    // 处理粗体和斜体
    html = this.renderEmphasis(html);
    
    // 处理链接
    html = this.renderLinks(html);
    
    // 处理列表
    html = this.renderLists(html);
    
    // 处理引用
    html = this.renderBlockquotes(html);
    
    // 处理表格
    html = this.renderTables(html);
    
    // 处理分隔线
    html = this.renderHorizontalRules(html);
    
    // 处理换行
    html = this.renderLineBreaks(html);
    
    return html;
  }

  /**
   * 转义HTML特殊字符
   * @param {string} text - 要转义的文本
   * @returns {string} 转义后的文本
   */
  escapeHtml(text) {
    const htmlEscapes = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
    };
    
    return text.replace(/[&<>"']/g, match => htmlEscapes[match]);
  }

  /**
   * 渲染代码块
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderCodeBlocks(text) {
    // 处理三个反引号的代码块
    return text.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
      const id = `code-block-${++this.codeBlockCounter}`;
      const lang = language || 'text';
      const escapedCode = code.trim();
      
      return `<div class="code-block" data-language="${lang}">
        <div class="code-header">
          <span class="code-language">${lang}</span>
          <button class="code-copy" onclick="copyCodeBlock('${id}')" title="复制代码">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </svg>
          </button>
        </div>
        <pre id="${id}"><code class="language-${lang}">${escapedCode}</code></pre>
      </div>`;
    });
  }

  /**
   * 渲染行内代码
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderInlineCode(text) {
    return text.replace(/`([^`]+)`/g, '<code>$1</code>');
  }

  /**
   * 渲染标题
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderHeaders(text) {
    return text.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, content) => {
      const level = hashes.length;
      return `<h${level}>${content.trim()}</h${level}>`;
    });
  }

  /**
   * 渲染粗体和斜体
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderEmphasis(text) {
    // 粗体 **text** 或 __text__
    text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    text = text.replace(/__([^_]+)__/g, '<strong>$1</strong>');
    
    // 斜体 *text* 或 _text_
    text = text.replace(/\*([^*]+)\*/g, '<em>$1</em>');
    text = text.replace(/_([^_]+)_/g, '<em>$1</em>');
    
    return text;
  }

  /**
   * 渲染链接
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderLinks(text) {
    // Markdown链接 [text](url)
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
    
    // 自动链接
    text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
    
    return text;
  }

  /**
   * 渲染列表
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderLists(text) {
    // 有序列表
    text = text.replace(/^(\d+\.\s+.+)(\n\d+\.\s+.+)*/gm, (match) => {
      const items = match.split('\n').map(line => {
        const content = line.replace(/^\d+\.\s+/, '');
        return `<li>${content}</li>`;
      }).join('');
      return `<ol>${items}</ol>`;
    });
    
    // 无序列表
    text = text.replace(/^([-*+]\s+.+)(\n[-*+]\s+.+)*/gm, (match) => {
      const items = match.split('\n').map(line => {
        const content = line.replace(/^[-*+]\s+/, '');
        return `<li>${content}</li>`;
      }).join('');
      return `<ul>${items}</ul>`;
    });
    
    return text;
  }

  /**
   * 渲染引用
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderBlockquotes(text) {
    return text.replace(/^>\s+(.+)$/gm, '<blockquote>$1</blockquote>');
  }

  /**
   * 渲染表格
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderTables(text) {
    return text.replace(/^\|(.+)\|\n\|[-\s|:]+\|\n((\|.+\|\n?)+)/gm, (match, header, separator, rows) => {
      const headerCells = header.split('|').map(cell => `<th>${cell.trim()}</th>`).join('');
      const rowsHtml = rows.trim().split('\n').map(row => {
        const cells = row.split('|').slice(1, -1).map(cell => `<td>${cell.trim()}</td>`).join('');
        return `<tr>${cells}</tr>`;
      }).join('');
      
      return `<table>
        <thead><tr>${headerCells}</tr></thead>
        <tbody>${rowsHtml}</tbody>
      </table>`;
    });
  }

  /**
   * 渲染分隔线
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderHorizontalRules(text) {
    return text.replace(/^(---+|===+|\*\*\*+)$/gm, '<hr>');
  }

  /**
   * 渲染换行
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderLineBreaks(text) {
    // 两个空格加换行符转换为<br>
    text = text.replace(/  \n/g, '<br>\n');
    
    // 段落分隔（两个换行符）
    text = text.replace(/\n\n/g, '</p><p>');
    
    // 包装在段落标签中
    if (text && !text.startsWith('<')) {
      text = `<p>${text}</p>`;
    }
    
    return text;
  }

  /**
   * 渲染数学公式（简单支持）
   * @param {string} text - 文本
   * @returns {string} 处理后的文本
   */
  renderMath(text) {
    // 行内数学公式 $formula$
    text = text.replace(/\$([^$]+)\$/g, '<span class="math-inline">$1</span>');
    
    // 块级数学公式 $$formula$$
    text = text.replace(/\$\$([^$]+)\$\$/g, '<div class="math-block">$1</div>');
    
    return text;
  }

  /**
   * 清理HTML
   * @param {string} html - HTML字符串
   * @returns {string} 清理后的HTML
   */
  sanitizeHtml(html) {
    // 允许的标签
    const allowedTags = [
      'p', 'br', 'strong', 'em', 'code', 'pre', 'a', 'ul', 'ol', 'li',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'table', 'thead',
      'tbody', 'tr', 'th', 'td', 'hr', 'div', 'span'
    ];
    
    // 简单的HTML清理（生产环境建议使用专门的库如DOMPurify）
    const div = document.createElement('div');
    div.innerHTML = html;
    
    // 移除不允许的标签
    const allElements = div.querySelectorAll('*');
    allElements.forEach(el => {
      if (!allowedTags.includes(el.tagName.toLowerCase())) {
        el.replaceWith(...el.childNodes);
      }
    });
    
    return div.innerHTML;
  }
}

/**
 * 复制代码块内容
 * @param {string} blockId - 代码块ID
 */
function copyCodeBlock(blockId) {
  const codeBlock = document.getElementById(blockId);
  if (codeBlock) {
    const code = codeBlock.textContent;
    copyToClipboard(code).then(success => {
      if (success) {
        showToast('代码已复制到剪贴板', 'success', 2000);
      } else {
        showToast('复制失败，请手动选择复制', 'error', 3000);
      }
    });
  }
}

// 创建全局Markdown渲染器实例
const markdownRenderer = new MarkdownRenderer();
