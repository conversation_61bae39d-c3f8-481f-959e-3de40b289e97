/* 当贝AI聊天界面 - 主样式文件 */

/* CSS变量定义 */
:root {
  /* 颜色系统 */
  --primary-color: #1677ff;
  --primary-hover: #4096ff;
  --primary-active: #0958d9;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  
  /* 中性色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-quaternary: #bfbfbf;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-quaternary: #f0f0f0;
  
  /* 边框色 */
  --border-color: #d9d9d9;
  --border-color-light: #f0f0f0;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 尺寸 */
  --sidebar-width: 280px;
  --header-height: 60px;
  --input-height: 120px;
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  --font-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  
  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 14px;
  line-height: 1.5;
}

body {
  font-family: var(--font-family);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-quaternary);
}

/* 主应用布局 */
.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* 顶部导航栏 */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  padding: 0 var(--spacing-lg);
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color-light);
  box-shadow: var(--shadow-sm);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.logo-icon {
  width: 28px;
  height: 28px;
  fill: var(--primary-color);
}

.logo-text {
  color: var(--text-primary);
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

.btn:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-color);
}

.btn:active {
  transform: translateY(1px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

/* 图标按钮 */
.sidebar-toggle,
.theme-toggle,
.settings-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover,
.theme-toggle:hover,
.settings-toggle:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

/* 主内容区域 */
.app-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 侧边栏 */
.sidebar {
  width: var(--sidebar-width);
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color-light);
  display: flex;
  flex-direction: column;
  transition: transform var(--transition-normal);
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--spacing-lg);
  gap: var(--spacing-md); /* 减少间距以节省空间 */
  overflow: hidden; /* 防止整个侧边栏滚动，让子元素各自处理滚动 */
}

/* 新建对话按钮 */
.new-chat-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-lg);
  background: var(--primary-color);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.new-chat-btn:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 模型选择器 */
.model-selector {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  flex-shrink: 0; /* 防止被压缩 */
}

.model-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
}

.model-select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.model-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.model-info {
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal) ease;
}

.model-info.expanded {
  max-height: 200px;
}

.model-desc {
  margin-bottom: var(--spacing-xs);
}

.model-features {
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

.model-badge,
.model-recommended {
  margin-top: var(--spacing-xs);
}

.badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-hot {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
}

.badge-new {
  background: linear-gradient(45deg, #4ecdc4, #6bcf7f);
  color: white;
}

.badge-recommended {
  background: linear-gradient(45deg, var(--primary-color), var(--primary-hover));
  color: white;
}

.badge-beta {
  background: linear-gradient(45deg, #ffa726, #ffcc02);
  color: white;
}

/* 对话选项 */
.chat-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm); /* 减少间距 */
  flex-shrink: 0; /* 防止被压缩 */
}

.options-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0;
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color-light);
}

.option-item {
  position: relative;
  background: var(--bg-primary);
  border: 1px solid var(--border-color-light);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  overflow: hidden;
}

.option-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
}

.option-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.option-item.disabled:hover {
  border-color: var(--border-color-light);
  box-shadow: none;
}

.option-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-md);
  margin: 0;
  position: relative;
  transition: all var(--transition-fast);
}

.option-label:hover {
  background: var(--bg-secondary);
}

.option-item.disabled .option-label {
  cursor: not-allowed;
}

.option-item.disabled .option-label:hover {
  background: transparent;
}

/* 自定义复选框 */
.option-checkbox {
  position: relative;
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
  opacity: 0;
}

.option-checkbox + .checkbox-custom {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-checkbox:checked + .checkbox-custom {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.option-checkbox:checked + .checkbox-custom::after {
  content: '';
  width: 4px;
  height: 8px;
  border: 2px solid white;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
  margin-top: -2px;
}

.option-checkbox:disabled + .checkbox-custom {
  background: var(--bg-quaternary);
  border-color: var(--border-color);
  cursor: not-allowed;
}

.option-content {
  flex: 1;
  margin-left: var(--spacing-lg);
}

.option-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.4;
}

.option-desc {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 2px 0 0 0;
  line-height: 1.3;
}

.option-item.disabled .option-text,
.option-item.disabled .option-desc {
  color: var(--text-quaternary);
}

/* 选项状态指示器 */
.option-status {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--success-color);
  opacity: 0;
  transform: scale(0);
  transition: all var(--transition-fast);
}

.option-checkbox:checked ~ .option-status {
  opacity: 1;
  transform: scale(1);
}

/* 选项激活动画 */
@keyframes optionActivate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.option-item.activating {
  animation: optionActivate 0.3s ease;
}

/* 复选框选中动画 */
@keyframes checkboxCheck {
  0% {
    transform: scale(0) rotate(0deg);
  }
  50% {
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}

.option-checkbox:checked + .checkbox-custom::after {
  animation: checkboxCheck 0.3s ease;
}

/* 模型信息展开动画 */
.model-info {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal) ease;
}

.model-info.expanded {
  max-height: 200px;
}

/* 悬浮效果增强 */
.option-item:hover {
  transform: translateY(-1px);
}

.option-item.disabled:hover {
  transform: none;
}

/* 会话列表 */
.sessions-section {
  flex: 1; /* 占用剩余空间 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm); /* 减少间距 */
  min-height: 0; /* 允许flex子项收缩 */
  overflow: hidden; /* 防止整个section滚动 */
}

.sessions-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0;
}

.sessions-list {
  flex: 1; /* 占用sessions-section的剩余空间 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  overflow-y: auto; /* 启用垂直滚动 */
  min-height: 0; /* 允许收缩 */
  padding-right: 2px; /* 为滚动条留出空间 */
}

/* 聊天区域 */
.chat-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  min-width: 0;
}

/* 消息容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  max-width: 800px;
  margin: 0 auto;
}

/* 欢迎消息 */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-xl);
  margin-top: 20vh;
}

.welcome-icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
}

.welcome-icon svg {
  width: 100%;
  height: 100%;
  fill: var(--primary-color);
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.welcome-desc {
  font-size: 16px;
  color: var(--text-secondary);
  max-width: 400px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --sidebar-width: 100%;
    --header-height: 56px;
  }
  
  .sidebar {
    position: fixed;
    top: var(--header-height);
    left: 0;
    height: calc(100vh - var(--header-height));
    z-index: 200;
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .app-main {
    width: 100%;
  }
  
  .chat-section {
    width: 100%;
  }
  
  .messages-container {
    padding: var(--spacing-md);
  }
  
  .welcome-message {
    margin-top: 10vh;
    padding: var(--spacing-lg);
  }
  
  .welcome-title {
    font-size: 20px;
  }
  
  .welcome-desc {
    font-size: 14px;
  }
}

/* 消息样式 */
.message {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: white;
  font-weight: 500;
  font-size: 12px;
}

.message.user .message-avatar {
  background: var(--success-color);
}

.message-content {
  flex: 1;
  max-width: calc(100% - 48px);
}

.message-bubble {
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color-light);
  word-wrap: break-word;
  line-height: 1.6;
}

.message.user .message-bubble {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.message-time {
  font-size: 11px;
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
  text-align: right;
}

.message.user .message-time {
  text-align: left;
}

/* 消息状态 */
.message-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 11px;
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

.message.user .message-status {
  justify-content: flex-start;
}

.status-icon {
  width: 12px;
  height: 12px;
  fill: currentColor;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  color: var(--text-secondary);
  font-style: italic;
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--text-tertiary);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.input-section {
  border-top: 1px solid var(--border-color-light);
  background: var(--bg-primary);
  padding: var(--spacing-lg);
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  transition: all var(--transition-fast);
  min-height: 52px; /* 确保最小高度 */
}

.input-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 16px; /* 增大字体以提高可读性 */
  line-height: 1.5; /* 24px行高 */
  resize: none;
  min-height: 28px; /* 调整最小高度：24px行高 + 4px内边距 */
  max-height: 120px; /* 最大高度约5行 */
  font-family: var(--font-family);
  padding: 2px 0; /* 垂直内边距 */
  overflow-y: hidden; /* 默认隐藏滚动条，由JavaScript控制 */
  transition: height 0.1s ease; /* 平滑的高度变化动画 */
}

.message-input::placeholder {
  color: var(--text-tertiary);
}

/* 确保输入框在不同浏览器中的一致性 */
.message-input::-webkit-input-placeholder {
  color: var(--text-tertiary);
}

.message-input::-moz-placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

.message-input:-ms-input-placeholder {
  color: var(--text-tertiary);
}

/* 防止iOS Safari的自动缩放 */
@supports (-webkit-touch-callout: none) {
  .message-input {
    font-size: 16px !important;
    transform: scale(1);
  }
}

/* 消息类型样式 */
.message-bubble.text-content {
  /* 正式回答内容 - 最显眼的主要内容区域 */
  background: var(--bg-secondary);
  border: 1px solid var(--border-color-light);
}

.message-bubble.progress-content {
  /* 进度消息 - 临时状态提示 */
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
}

/* 进度消息样式 */
.progress-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-style: italic;
}

.progress-text {
  color: var(--primary-color);
  font-weight: 500;
}

.progress-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-icon .icon {
  width: 16px;
  height: 16px;
  fill: var(--primary-color);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.send-button:disabled {
  background: var(--bg-quaternary);
  color: var(--text-quaternary);
  cursor: not-allowed;
}

.send-button.sending {
  background: var(--error-color);
}

.send-button .icon-stop {
  display: none;
}

.send-button.sending .icon-send {
  display: none;
}

.send-button.sending .icon-stop {
  display: block;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
  font-size: 12px;
  color: var(--text-tertiary);
}

.char-count {
  font-variant-numeric: tabular-nums;
}

.input-tips {
  display: flex;
  gap: var(--spacing-md);
}

@media (max-width: 480px) {
  .app-header {
    padding: 0 var(--spacing-md);
  }

  .logo-text {
    display: none;
  }

  .messages-container {
    padding: var(--spacing-sm);
  }

  .sidebar-content {
    padding: var(--spacing-md);
  }

  .input-section {
    padding: var(--spacing-md);
  }

  .input-tips {
    display: none;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }

  .message-content {
    max-width: calc(100% - 40px);
  }

  /* 移动端输入框优化 */
  .input-wrapper {
    padding: var(--spacing-sm);
    min-height: 48px;
  }

  .message-input {
    font-size: 16px; /* 防止iOS缩放 */
  }

  .send-button {
    width: 36px;
    height: 36px;
  }

  /* 移动端Toast优化 */
  .toast-container {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
  }

  .toast {
    max-width: none;
    min-width: auto;
    transform: translateY(-100%);
  }

  .toast.show {
    transform: translateY(0);
  }
}

/* 会话项样式 */
.session-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.session-item:hover {
  background: var(--bg-tertiary);
}

.session-item.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.session-icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
  flex-shrink: 0;
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-preview {
  font-size: 11px;
  color: var(--text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.session-item.active .session-preview {
  color: rgba(255, 255, 255, 0.8);
}

.session-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.session-item:hover .session-actions {
  opacity: 1;
}

.session-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: var(--radius-sm);
  background: transparent;
  color: currentColor;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.session-action:hover {
  background: rgba(0, 0, 0, 0.1);
}

.session-item.active .session-action:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 设置面板 */
.settings-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 320px;
  height: 100vh;
  background: var(--bg-primary);
  border-left: 1px solid var(--border-color-light);
  box-shadow: var(--shadow-xl);
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  z-index: 300;
}

.settings-panel.open {
  transform: translateX(0);
}

.settings-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
}

.settings-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.settings-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.settings-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.settings-body {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.setting-group {
  margin-bottom: var(--spacing-xl);
}

.setting-group h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
}

.setting-item label {
  font-size: 14px;
  color: var(--text-primary);
  cursor: pointer;
}

.setting-item select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 13px;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  margin-right: var(--spacing-sm);
  accent-color: var(--primary-color);
}

/* 加载和错误状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--bg-quaternary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Toast容器 */
.toast-container {
  position: fixed;
  top: calc(var(--header-height) + var(--spacing-lg));
  right: var(--spacing-lg);
  z-index: 1001;
  pointer-events: none; /* 允许点击穿透到下层元素 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: calc(100vh - var(--header-height) - var(--spacing-xl));
  overflow: hidden;
}

/* Toast基础样式 */
.toast {
  max-width: 400px;
  min-width: 300px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  transform: translateX(calc(100% + var(--spacing-lg)));
  transition: transform var(--transition-normal);
  pointer-events: auto; /* 恢复toast本身的点击事件 */
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.toast.show {
  transform: translateX(0);
}

/* 不同类型的toast样式 */
.toast-error {
  border: 1px solid var(--error-color);
}

.toast-success {
  border: 1px solid var(--success-color);
}

.toast-warning {
  border: 1px solid var(--warning-color);
}

.toast-info {
  border: 1px solid var(--primary-color);
}

.toast-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  min-height: 48px;
}

.toast-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.toast-error .toast-icon {
  fill: var(--error-color);
}

.toast-success .toast-icon {
  fill: var(--success-color);
}

.toast-warning .toast-icon {
  fill: var(--warning-color);
}

.toast-info .toast-icon {
  fill: var(--primary-color);
}

.toast-message {
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 280px;
}

.toast-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: var(--radius-sm);
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  flex-shrink: 0;
  margin-top: -2px;
}

.toast-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 思考过程样式 */
.thinking-content {
  margin-top: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  overflow: hidden;
}

.thinking-content.collapsed .thinking-body {
  display: none;
}

.thinking-content.collapsed .thinking-toggle {
  transform: rotate(-90deg);
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid var(--border-color);
}

.thinking-header:hover {
  background: var(--bg-hover);
}

.thinking-icon {
  font-size: 1.1em;
}

.thinking-title {
  flex: 1;
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.9em;
}

.thinking-toggle {
  transition: transform 0.2s ease;
  color: var(--text-tertiary);
  font-size: 0.8em;
}

.thinking-body {
  padding: var(--spacing-md);
  font-size: 0.9em;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 卡片内容样式 */
.card-content {
  margin-top: var(--spacing-md);
}

.search-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.card-title {
  margin: 0;
  font-size: 1.1em;
  font-weight: 600;
  color: var(--text-primary);
}

.card-subtitle {
  margin: var(--spacing-xs) 0 0 0;
  font-size: 0.9em;
  color: var(--text-secondary);
}

.card-body {
  padding: var(--spacing-md);
}

.card-item {
  margin-bottom: var(--spacing-lg);
}

.card-item:last-child {
  margin-bottom: 0;
}

.item-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1em;
  font-weight: 500;
  color: var(--text-primary);
}

/* 搜索词样式 */
.search-terms-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.search-term {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-color);
  color: white;
  border-radius: var(--radius-sm);
  font-size: 0.85em;
  font-weight: 500;
}

/* 引用列表样式 */
.references-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.reference-item {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  transition: all var(--transition-fast);
}

.reference-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reference-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.reference-index {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  font-size: 0.8em;
  font-weight: 600;
  flex-shrink: 0;
}

.reference-title {
  flex: 1;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95em;
  line-height: 1.3;
}

.reference-title:hover {
  text-decoration: underline;
}

.reference-favicon {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-xs);
  flex-shrink: 0;
}

.reference-snippet {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9em;
  line-height: 1.4;
  margin-bottom: var(--spacing-xs);
}

.reference-site {
  display: inline-block;
  padding: 2px var(--spacing-xs);
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  border-radius: var(--radius-xs);
  font-size: 0.8em;
}

.reference-error {
  color: var(--error-color);
  font-style: italic;
}

.card-raw-data {
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.85em;
  color: var(--text-secondary);
  overflow-x: auto;
}

/* 底部对话选项样式 */
.chat-options-bottom {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  justify-content: center;
}

.chat-options-bottom .option-item {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}

.chat-options-bottom .option-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.chat-options-bottom .option-label:hover {
  border-color: var(--primary-color);
  background: var(--bg-hover);
}

.chat-options-bottom .option-checkbox {
  display: none;
}

.chat-options-bottom .checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-xs);
  background: var(--bg-primary);
  position: relative;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.chat-options-bottom .option-checkbox:checked + .checkbox-custom {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.chat-options-bottom .option-checkbox:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.chat-options-bottom .option-content {
  flex: 1;
}

.chat-options-bottom .option-text {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9em;
  margin-bottom: 2px;
}

.chat-options-bottom .option-desc {
  font-size: 0.8em;
  color: var(--text-secondary);
  line-height: 1.3;
}

.chat-options-bottom .option-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-tertiary);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.chat-options-bottom .option-checkbox:checked ~ .option-status {
  background: var(--success-color);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-options-bottom {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .chat-options-bottom .option-item {
    min-width: auto;
    max-width: none;
  }
}
